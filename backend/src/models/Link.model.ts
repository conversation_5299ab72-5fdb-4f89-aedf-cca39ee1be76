import mongoose, { Document, Schema } from 'mongoose';

export interface ShuffleCard {
  imageUrl: string;
  title: string;
  tag?: string;
  url: string;
}

export interface MiniLeadField {
  type: 'email' | 'phone' | 'activity';
  required: boolean;
  label?: string;
}

export interface ILink extends Document {
  userId: mongoose.Types.ObjectId;
  type: 'link' | 'announcement' | 'shuffle' | 'minilead' | 'video' | 'embedded' | 'lottie';
  title: string;
  url: string;
  icon?: string;
  order: number;
  clickCount: number;
  archived: boolean;
  enabled: boolean;
  badge?: {
    text: string;
    backgroundColor: string;
    textColor: string;
    animation?: 'popup' | 'shake' | 'vibrate';
    animationDelay?: number;
  };
  // Advanced options
  password?: string;
  passwordEnabled?: boolean;
  passwordExpiryDate?: Date;
  visibilityDates?: {
    startDate?: Date;
    endDate?: Date;
    enabled?: boolean;
  };
  // Announcement specific fields
  backgroundColor?: string;
  backgroundGradient?: {
    color1: string;
    color2: string;
    direction: string;
  };
  useGradient?: boolean;
  textColor?: string;
  text?: string;
  titleAlignment?: 'left' | 'center' | 'right';
  textAlignment?: 'left' | 'center' | 'right';
  buttonText?: string;
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  // Shuffle cards specific fields
  shuffleCards?: ShuffleCard[];
  // MiniLead specific fields
  introText?: string;
  fields?: MiniLeadField[];
  // Video specific fields
  videoUrl?: string;
  thumbnailUrl?: string;
  orientation?: 'portrait' | 'landscape';
  duration?: number;
  videoId?: string; // Bunny CDN video ID
  // Embedded video specific fields
  youtubeCode?: string;
  tikTokUrl?: string;
  embedType?: 'youtube' | 'tiktok' | 'stream';
  autoplay?: boolean;
  muted?: boolean;
  showControls?: boolean;
  // Lottie animation specific fields
  lottieUrl?: string;
  triggerEvent?: 'pageLoad' | 'inactivity' | 'linkClick';
  triggerDelay?: number;
  linkId?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ShuffleCardSchema = new Schema({
  imageUrl: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  tag: {
    type: String,
    trim: true,
  },
  url: {
    type: String,
    required: true,
    trim: true,
  },
}, { _id: false });

const MiniLeadFieldSchema = new Schema({
  type: {
    type: String,
    enum: ['email', 'phone', 'activity'],
    required: true,
  },
  required: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    trim: true,
  },
}, { _id: false });

const LinkSchema = new Schema<ILink>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    type: {
      type: String,
      enum: ['link', 'announcement', 'shuffle', 'minilead', 'video', 'embedded', 'lottie'],
      default: 'link',
      required: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      required: function(this: ILink) {
        return this.type === 'link' || this.type === 'announcement';
      },
      trim: true,
    },
    icon: {
      type: String,
      trim: true,
    },
    order: {
      type: Number,
      default: 0,
    },
    clickCount: {
      type: Number,
      default: 0,
    },
    archived: {
      type: Boolean,
      default: false,
    },
    enabled: {
      type: Boolean,
      default: true,
    },
    badge: {
      type: {
        text: {
          type: String,
          trim: true,
        },
        backgroundColor: {
          type: String,
          default: 'rgba(59, 130, 246, 0.2)', // Light blue with transparency
        },
        textColor: {
          type: String,
          default: '#3b82f6', // Blue
        },
        animation: {
          type: String,
          enum: ['popup', 'shake', 'vibrate'],
          default: 'popup',
        },
        animationDelay: {
          type: Number,
          default: 3000, // 3 seconds
        },
      },
      default: null,
    },
    // Announcement specific fields
    backgroundColor: {
      type: String,
      default: '#f3f4f6', // Light gray
    },
    backgroundGradient: {
      type: {
        color1: {
          type: String,
          default: '#8B5CF6', // Purple
        },
        color2: {
          type: String,
          default: '#EC4899', // Pink
        },
        direction: {
          type: String,
          default: 'to right', // CSS gradient direction
        },
      },
      default: null,
    },
    useGradient: {
      type: Boolean,
      default: false,
    },
    textColor: {
      type: String,
      default: '#000000', // Black
    },
    text: {
      type: String,
      trim: true,
    },
    titleAlignment: {
      type: String,
      enum: ['left', 'center', 'right'],
      default: 'left',
    },
    textAlignment: {
      type: String,
      enum: ['left', 'center', 'right'],
      default: 'left',
    },
    buttonText: {
      type: String,
      trim: true,
    },
    buttonBackgroundColor: {
      type: String,
      default: '#3b82f6', // Blue
    },
    buttonTextColor: {
      type: String,
      default: '#ffffff', // White
    },
    // Shuffle cards specific fields
    shuffleCards: {
      type: [ShuffleCardSchema],
      default: undefined,
      validate: {
        validator: function(cards: ShuffleCard[]) {
          return !cards || cards.length <= 3;
        },
        message: 'Maximum 3 shuffle cards allowed'
      }
    },
    // MiniLead specific fields
    introText: {
      type: String,
      trim: true,
    },
    fields: {
      type: [MiniLeadFieldSchema],
      default: undefined,
    },
    // Video specific fields
    videoUrl: {
      type: String,
      trim: true,
    },
    thumbnailUrl: {
      type: String,
      trim: true,
    },
    orientation: {
      type: String,
      enum: ['portrait', 'landscape'],
      default: 'landscape',
    },
    duration: {
      type: Number,
      default: 0,
    },
    videoId: {
      type: String,
      trim: true,
    },
    // Embedded video specific fields
    youtubeCode: {
      type: String,
      trim: true,
    },
    tikTokUrl: {
      type: String,
      trim: true,
    },
    embedType: {
      type: String,
      enum: ['youtube', 'tiktok', 'stream'],
      default: 'youtube',
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
    muted: {
      type: Boolean,
      default: true,
    },
    showControls: {
      type: Boolean,
      default: true,
    },
    // Advanced options
    password: {
      type: String,
      trim: true,
    },
    passwordEnabled: {
      type: Boolean,
      default: false,
    },
    passwordExpiryDate: {
      type: Date,
      default: null,
    },
    visibilityDates: {
      type: {
        startDate: {
          type: Date,
          default: null,
        },
        endDate: {
          type: Date,
          default: null,
        },
        enabled: {
          type: Boolean,
          default: false,
        },
      },
      default: null,
    },
    // Lottie animation specific fields
    lottieUrl: {
      type: String,
      trim: true,
    },
    triggerEvent: {
      type: String,
      enum: ['pageLoad', 'inactivity', 'linkClick'],
      default: 'pageLoad',
    },
    triggerDelay: {
      type: Number,
      default: 0, // Delay in seconds
    },
    linkId: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model<ILink>('Link', LinkSchema);
