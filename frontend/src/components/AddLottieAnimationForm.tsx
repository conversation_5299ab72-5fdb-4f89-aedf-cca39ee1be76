import { useState, useEffect } from 'react';
import { Link } from '../api/links';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { preloadLottieAnimation } from './LottiePopup';

interface AddLottieAnimationFormProps {
  onAdd: (
    title: string,
    lottieUrl: string,
    triggerEvent: 'pageLoad' | 'inactivity' | 'linkClick',
    triggerDelay: number,
    linkId?: string
  ) => void;
  activeLinks: Link[];
}

const AddLottieAnimationForm = ({ onAdd, activeLinks }: AddLottieAnimationFormProps) => {
  const [title, setTitle] = useState('');
  const [lottieUrl, setLottieUrl] = useState('');
  const [triggerEvent, setTriggerEvent] = useState<'pageLoad' | 'inactivity' | 'linkClick'>('pageLoad');
  const [triggerDelay, setTriggerDelay] = useState(0);
  const [linkId, setLinkId] = useState<string>('');
  const [showLinkSelector, setShowLinkSelector] = useState(false);
  const [preloadStatus, setPreloadStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Reset link selector visibility when trigger event changes
  useEffect(() => {
    setShowLinkSelector(triggerEvent === 'linkClick');
    if (triggerEvent !== 'linkClick') {
      setLinkId('');
    }
  }, [triggerEvent]);

  // Preload animation when URL changes
  useEffect(() => {
    // Don't try to preload if URL is empty or too short
    if (!lottieUrl || lottieUrl.length < 10) {
      setPreloadStatus('idle');
      return;
    }

    // Debounce the preloading to avoid unnecessary requests while typing
    const debounceTimer = setTimeout(async () => {
      try {
        setPreloadStatus('loading');

        // Attempt to preload the animation
        await preloadLottieAnimation(lottieUrl);

        setPreloadStatus('success');
        console.log('Animation preloaded successfully:', lottieUrl);
      } catch (error) {
        console.error('Failed to preload animation:', error);
        setPreloadStatus('error');
      }
    }, 800); // Wait 800ms after the user stops typing

    return () => clearTimeout(debounceTimer);
  }, [lottieUrl]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      alert('Please enter a title');
      return;
    }

    if (!lottieUrl.trim()) {
      alert('Please enter a Lottie animation URL');
      return;
    }

    if (triggerEvent === 'linkClick' && !linkId) {
      alert('Please select a link to trigger the animation');
      return;
    }

    // Log the data being sent to ensure it's complete
    console.log('Adding lottie animation with data:', {
      title,
      lottieUrl,
      triggerEvent,
      triggerDelay,
      linkId: triggerEvent === 'linkClick' ? linkId : undefined
    });

    onAdd(
      title,
      lottieUrl,
      triggerEvent,
      triggerDelay,
      triggerEvent === 'linkClick' ? linkId : undefined
    );

    // Reset form
    setTitle('');
    setLottieUrl('');
    setTriggerEvent('pageLoad');
    setTriggerDelay(0);
    setLinkId('');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Animation Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="lottieUrl">Lottie Animation URL</Label>
        <div className="relative">
          <Input
            id="lottieUrl"
            value={lottieUrl}
            onChange={(e) => setLottieUrl(e.target.value)}
            placeholder="https://example.com/animation.json"
            className={`${
              preloadStatus === 'success' ? 'border-green-500 pr-10' :
              preloadStatus === 'error' ? 'border-red-500 pr-10' :
              preloadStatus === 'loading' ? 'pr-10' : ''
            }`}
          />
          {preloadStatus === 'loading' && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
            </div>
          )}
          {preloadStatus === 'success' && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M20 6L9 17l-5-5"></path>
              </svg>
            </div>
          )}
          {preloadStatus === 'error' && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground">
          Enter the URL to a Lottie JSON file or a hosted Lottie animation
          {preloadStatus === 'success' && (
            <span className="text-green-500 ml-1">• Animation preloaded and ready</span>
          )}
          {preloadStatus === 'error' && (
            <span className="text-red-500 ml-1">• Failed to preload animation, check URL</span>
          )}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="triggerEvent">Trigger Event</Label>
        <Select
          value={triggerEvent}
          onValueChange={(value) => setTriggerEvent(value as 'pageLoad' | 'inactivity' | 'linkClick')}
        >
          <SelectTrigger id="triggerEvent">
            <SelectValue placeholder="Select trigger event" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pageLoad">Page Load</SelectItem>
            <SelectItem value="inactivity">User Inactivity</SelectItem>
            <SelectItem value="linkClick">Link Click</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="triggerDelay">
          {triggerEvent === 'pageLoad'
            ? 'Delay after page load (seconds)'
            : triggerEvent === 'inactivity'
              ? 'Inactivity time before trigger (seconds)'
              : 'Delay after link click (seconds)'}
        </Label>
        <div className="flex items-center gap-4">
          <Slider
            id="triggerDelay"
            value={[triggerDelay]}
            min={0}
            max={triggerEvent === 'inactivity' ? 60 : 10}
            step={1}
            onValueChange={(value: number[]) => setTriggerDelay(value[0])}
            className="flex-1"
          />
          <span className="w-12 text-center">{triggerDelay}s</span>
        </div>
      </div>

      {showLinkSelector && (
        <div className="space-y-2">
          <Label htmlFor="linkId">Select Link to Trigger Animation</Label>
          <Select
            value={linkId || "placeholder"}
            onValueChange={setLinkId}
          >
            <SelectTrigger id="linkId">
              <SelectValue placeholder="Select a link" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="placeholder" disabled>Select a link</SelectItem>
              {activeLinks.length === 0 ? (
                <SelectItem value="no-links" disabled>No links available</SelectItem>
              ) : (
                activeLinks.map((link) => (
                  <SelectItem key={link._id} value={link._id}>
                    {link.title}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      <Button type="submit" className="w-full">
        Add Lottie Animation
      </Button>
    </form>
  );
};

export default AddLottieAnimationForm;
